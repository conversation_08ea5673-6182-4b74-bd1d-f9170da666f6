#!/usr/bin/env zsh

# 强制终端支持 256 色
export TERM=xterm-256color

# 设置 fzf 默认选项
export FZF_DEFAULT_OPTS="
  --height 40% --reverse --border
  --prompt=': '
  --color=fg:252,fg+:37,bg:-1,bg+:-1
  --color=border:37,pointer:bold:37,marker:bold:37
  --pointer='>' --marker='✔'
"

SSH_CONFIG="${HOME}/.ssh/config"
SSH_CONFIG_DIR="${HOME}/.ssh/config.d"
hosts=()
groups=()
# 读取config.dz中分组下的所有主机和分组信息并按照分组存放到数据
function get_hosts_groups()
{
    # 检查配置文件和目录是否存在
    if [ ! -f $SSH_CONFIG ] && [ ! -d $SSH_CONFIG_DIR ]; then
        echo "错误：找不到 ~/.ssh/config 或 ~/.ssh/config.d 目录"
        exit 1
    fi

    for config_file in $SSH_CONFIG_DIR/*; do
        if [ -f $config_file ]; then
            local group_name=$(basename $config_file)
            groups+=("$group_name")  # 添加分组
            while read -r host ip; do
                hosts+=("$host $ip")  # 添加分组中的主机
            done < <(awk '
                $1 == "Host" {host=$2}
                $1 == "HostName" {print host " " $2}
            ' $config_file | sort -u)
        fi
    done

    # 检查是否找到内容
    if [ ${#hosts[@]} -eq 0 ] && [ ${#groups[@]} -eq 0 ]; then
        echo "错误：未找到任何内容"
        exit 1
    fi
}

function select_row()
{
    # 默认界面：显示所有主机，最后显示一个 group 选项
    local items=("${hosts[@]}")
    items+=("group")

    # 使用 fzf 选择主机或分组
    local selected=$(printf "%s\n" "${items[@]}" | fzf --exact --header "↑↓ 导航 | Enter 选择主机或分组")

    # 检查是否选择了内容
    if [ -z "$selected" ]; then
        exit 1
    fi

    if [[ "$selected" == "group" ]]; then
        select_group
    else
        select_hosts "$selected"
    fi
}

function select_hosts()
{
    # 获取 IP 地址
    local ip=$(echo "$1" | awk '{print $2}')

    # 执行 sftp ip
    echo "正在sftp连接: $ip"
    sftp "$ip"
}

function select_group()
{
    # 分组列表界面：显示所有分组
    local selected=$(printf "%s\n" "${groups[@]}" | fzf --exact --header "↑↓ 导航 | Enter 选择分组")

    # 检查是否选择了分组
    if [ -z "$selected" ]; then
        exit 1
    fi

    # 读取该分组的主机,重复读取了group中的分组信息
    local group_hosts=()
    while read -r host ip; do
        group_hosts+=("$host $ip")
    done < <(awk '
      $1 == "Host" {host=$2}
      $1 == "HostName" {print host " " $2}
    ' "$SSH_CONFIG_DIR/$selected" | sort -u)

    if [ ${#group_hosts[@]} -eq 0 ]; then
        echo "错误：分组 '$selected' 中未找到任何主机"
        exit 1
    fi

    # 使用 fzf 选择分组中的主机
    selected=$(printf "%s\n" "${group_hosts[@]}" | fzf --exact --header "↑↓ 导航 | Enter 选择主机")

    # 检查是否选择了主机
    if [ -z "$selected" ]; then
        exit 1
    fi

    # 获取 IP 地址
    select_hosts "$selected"

}

function main()
{
    get_hosts_groups
    select_row
}

main
