#!/usr/bin/env zsh

SSH_CONFIG_DIR="$HOME/.ssh/config.d"

# 确保 `config.d` 目录存在
if [[ ! -d "$SSH_CONFIG_DIR" ]]; then
    echo "❌ Error: SSH config directory $SSH_CONFIG_DIR not found."
    exit 1
fi

# 统一 fzf 主题
export FZF_DEFAULT_OPTS="--height 40% --reverse --border \
    --prompt='Select action: ' --color=fg:252,fg+:37,bg:-1,bg+:-1 \
    --color=border:37,pointer:bold:37,marker:bold:37 \
    --pointer='>' --marker='✔'"

# 选择 `add` / `update` / `delete`
action=""
all_hosts=()
groups=()
declare -A selected_row
# group 中的 host信息
declare -A entries_of_group
# 通过host找到group
declare -A entry_to_group

# 获取hosts groups entry_to_group
function load_ssh_config()
{
    # 检查配置文件和目录是否存在
    if [ ! -d $SSH_CONFIG_DIR ]; then
        echo "错误：找不到 ~/.ssh/config.d 目录"
        exit 1
    fi

    for config_file in $SSH_CONFIG_DIR/*; do
        if [ -f $config_file ]; then
            local group_name=$(basename $config_file)
            groups+=("$group_name")  # 添加分组
            local entries=()
            while read -r host ip; do
                all_hosts+=("$host $ip")  # 添加分组中的主机
                entries+=("$host $ip")
                entry_to_group["$host $ip"]="$group_name"
            done < <(awk '
                $1 == "Host" {host=$2}
                $1 == "HostName" {print host " " $2}
            ' $config_file | sort -u)
            entries_of_group["$group_name"]=$entries
        fi
    done

    # 检查是否找到内容
    if [ ${#all_hosts[@]} -eq 0 ] && [ ${#groups[@]} -eq 0 ]; then
        echo "错误：未找到任何内容"
        exit 1
    fi

    all_hosts+="group"  # 添加分组中的主机
}

function select_host()
{
    local selected=$(printf "%s\n" "${all_hosts[@]}" | fzf --exact --prompt="Select SSH entry or group: ") || exit 0

    # 如果选择 `group`，进入分组详情
    if [[ "$selected" == "group" ]]; then
        local select_group=$(printf "%s\n" "${groups[@]}" | fzf --prompt="Select group: ") || exit 0
        file_name="$SSH_CONFIG_DIR/$select_group"

        # 选择组内 `hostname ip`
        entry=$(printf "%s\n" "$entry_to_group["$select_group"]" | fzf --exact --prompt="Select host in group: ") || exit 0
        selected_row["file"]="$file_name"
    else
        selected_row["file"]=${entry_to_group["$selected"]}
    fi

    selected_row["host"]=$(echo "$selected" | awk '{print $1}')
    selected_row["ip"]=$(echo "$selected" | awk '{print $2}')
}

function select_action()
{
    action=$(echo -e "add\nupdate\ndelete" | fzf --exact) || exit 0
}

function add_entry()
{
    # 选择 `group`
    local config_file=$(ls "$SSH_CONFIG_DIR" | fzf --exact --prompt="Select group: ") || exit 0
    config_file="$SSH_CONFIG_DIR/$config_file"

    # 输入 Hostname, IP 和 User
    read -r "hostname?hostname: "
    read -r "ip?ip address: "
    read -r "user?user (default: root): "

    [[ -z "$user" ]] && user="root"

    # 传输 SSH 证书
    echo "🔑 Transferring SSH key..."
    ssh-copy-id "$user@$ip" >/dev/null 2>&1
    if [[ $? -ne 0 ]]; then
        echo "❌ Error: SSH key transfer failed."
        exit 1
    fi

    local target="$user@$ip"

    echo "🔧 Modifying ~/.bashrc on remote server..."
    ssh -q "$target" <<'EOF'
        # 设置 PS1 变量，显示 "用户名@IP$ "
        ps1_line='PS1="[\u@$(hostname -I | awk '\''{print $1}'\'')]\$(if [ \"\$(id -u)\" -eq 0 ]; then echo \"#\"; else echo \"\$\"; fi) "'

        # 设置 PROMPT_COMMAND，修改终端标签页
        line_to_add='PROMPT_COMMAND="echo -ne \"\033]0;$USER@$(hostname -I | cut -d'\"' '\"' -f1)\007\""'

        # 如果 ~/.bashrc 中没有 PS1 这一行，则添加
        if ! grep -Fxq "$ps1_line" ~/.bashrc; then
            echo "$ps1_line" >> ~/.bashrc
        fi

        # 如果 ~/.bashrc 中没有 PROMPT_COMMAND 这一行，则添加
        if ! grep -Fxq "$line_to_add" ~/.bashrc; then
            echo "$line_to_add" >> ~/.bashrc
        fi

        # 立即生效
        source ~/.bashrc
EOF

    # 写入 SSH 配置
    echo -e "\nHost $hostname\n    HostName $ip\n    User $user" >> "$config_file"
    echo "✅ Successfully added $hostname to $config_file"
}

function update_entry()
{
    load_ssh_config
    select_host

    ssh_user=$(awk "/^Host ${selected_row["host"]}\$/,/^User /" $SSH_CONFIG_DIR/${selected_row["file"]} | awk '/^    User / {print $2}')
    [[ -z "${ssh_user}" ]] && ssh_user="root"

    local target="$ssh_user@${selected_row["ip"]}"
    # 重新传输 SSH 证书
    echo " "$selected_row["file"]": $target"
    echo "🔑 Updating SSH key..."
    ssh-copy-id "$target" >/dev/null 2>&1
    if [[ $? -ne 0 ]]; then
        echo "❌ Error: SSH key update failed.  Check the connection and user permissions."  # 更详细的错误信息
        exit 1
    fi

    # 🔧 修改远程 `~/.bashrc`
    echo "🔧 Modifying ~/.bashrc on remote server..."
    ssh -q "$target" <<'EOF'
        # 设置 PS1 变量，显示 "用户名@IP$ "
        ps1_line='PS1="[\u@$(hostname -I | awk '\''{print $1}'\'')]\$(if [ \"\$(id -u)\" -eq 0 ]; then echo \"#\"; else echo \"\$\"; fi) "'

        # 设置 PROMPT_COMMAND，修改终端标签页
        line_to_add='PROMPT_COMMAND="echo -ne \"\033]0;$USER@$(hostname -I | cut -d'\"' '\"' -f1)\007\""'

        # 如果 ~/.bashrc 中没有 PS1 这一行，则添加
        if ! grep -Fxq "$ps1_line" ~/.bashrc; then
            echo "$ps1_line" >> ~/.bashrc
        fi

        # 如果 ~/.bashrc 中没有 PROMPT_COMMAND 这一行，则添加
        if ! grep -Fxq "$line_to_add" ~/.bashrc; then
            echo "$line_to_add" >> ~/.bashrc
        fi

        # 立即生效
        source ~/.bashrc
EOF

    echo "✅ Successfully updated SSH key and modified ~/.bashrc for ${selected_row["host"]}"
}

function delete_entry()
{
    load_ssh_config
    select_host

    local config_file="$SSH_CONFIG_DIR""/""${selected_row["file"]}"

    [[ ! -f "$config_file" ]] && exit 0
    # **彻底删除 `Host` 记录**
    awk -v host="${selected_row["host"]}" '
        /^Host / {if ($2 == host) skip=1; else skip=0}
        skip {next}
        !/^[[:space:]]*$/ # 彻底删除空白行
    ' $config_file > "$config_file.tmp" && mv "$config_file.tmp" $config_file

    echo "Successfully deleted ${selected_row["ip"]} from $config_file"
}

function main()
{
    # get_hosts_groups  # 还能再优化,add只需要读取group
    select_action
    case "$action" in
      "add")
        add_entry
        ;;
      "update")
        update_entry
        ;;
      "delete")
        delete_entry
        ;;
      *)
        echo "无效的 action: $action"
        echo "请使用 add, update 或 delete"
        exit 1
        ;;
    esac
}

main
